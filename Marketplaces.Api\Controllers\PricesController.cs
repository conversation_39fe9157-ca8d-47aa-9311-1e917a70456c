using AutoMapper;
using Marketplaces.Api.Authorization;
using Marketplaces.Api.Clients;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Dtos.Internal;
using Marketplaces.Api.Exceptions;
using Marketplaces.Api.Models;
using Marketplaces.Api.Requests;
using Marketplaces.Api.Responses;
using Marketplaces.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Net;
using System.Net.Mime;
using System.Text.Json;
using PriceDto = Marketplaces.Api.Responses.PriceDto;

namespace Marketplaces.Api.Controllers;

[ApiController]
[Route("shops/current/[controller]")]
[Authorize(Policy = Policies.ActiveSubscription)]
[Consumes(MediaTypeNames.Application.Json)]
[Produces(MediaTypeNames.Application.Json)]
public class PricesController(DatabaseContext context,
    WildberriesClient wbClient, OzonClient ozonClient, YandexClient yandexClient, IMapper mapper,
    ILogger<PricesController> logger, DiscountCalculationService discountCalculationService) : ShopAbstractController(context)
{
    [HttpPost("sync")]
    public async Task<List<PriceDto>> SyncPrices()
    {
        // todo: leave only sync part and not return from this place
        var shop = await GetShop();
        var nomenclatures = await Context.FullNomenclatures.Where(n => n.ShopId == shop.Id)
            .OrderBy(n => n.Name)
            .ToListAsync();

        if (shop.ContainsToken(Marketplace.Ozon))
        {
            var ozonNomenclatures = nomenclatures
                .Where(n => n.Ozon != null)
                .Select(n => n.Ozon!).ToList();

            var ozonPrices = await ozonClient.GetPrices(shop, [.. ozonNomenclatures.Select(ozon => ozon.Code)]);

            NomenclatureAggregator.UpsertPrices(ozonNomenclatures, ozonPrices);
        }

        var wbAuthenticationData = shop.GetToken(Marketplace.Wildberries);
        if (wbAuthenticationData != null)
        {
            var wbNomenclatures = nomenclatures.Where(n => n.Wildberries != null).ToList();
            var wbPrices = await wbClient.GetPrices(wbAuthenticationData,
                [.. wbNomenclatures.Select(wb => wb.Wildberries!.Id)]);
            NomenclatureAggregator.UpsertPrices(wbNomenclatures, wbPrices);
        }

        var yandexAuthenticationData = shop.GetToken(Marketplace.Yandex);
        if (yandexAuthenticationData != null)
        {
            var yandexNomenclatures = nomenclatures.Where(n => n.Yandex != null).ToList();
            var yandexPrices = await yandexClient.GetPrices(yandexAuthenticationData, [.. yandexNomenclatures
                .Select(wb => wb.Yandex?.Code).OfType<string>()]);
            NomenclatureAggregator.UpsertPrices(yandexNomenclatures, yandexPrices);
        }

        shop.RecordLastPriceSync();
        await Context.SaveChangesAsync();
        var prices = mapper.Map<List<PriceDto>>(nomenclatures);

        for (int i = 0; i < prices.Count; i++)
        {
            var nomenclature = nomenclatures.ElementAtOrDefault(i);
            if (nomenclature != null)
            {
                prices[i].CalculateProfitFields(nomenclature, shop);
            }
        }

        return prices;
    }

    [HttpPost("shapshot")]
    public async Task<IActionResult> MakeSnapshot()
    {
        var shop = await GetShop();
        var nomenclatures = await Context.FullNomenclatures.Where(n => n.ShopId == shop.Id)
            .OrderBy(n => n.Name)
            .ToListAsync();

        var prices = mapper.Map<List<PriceDto>>(nomenclatures);
        var data = JsonSerializer.Serialize(prices, options: new JsonSerializerOptions()
        {
            WriteIndented = true
        });
        var snapshot = await Context.Set<PriceSnapshot>().FindAsync(shop.Id);
        if (snapshot is null)
        {
            snapshot = new PriceSnapshot(shop.Id, data);
            await Context.AddAsync(snapshot);
        }
        else
        {
            snapshot.Update(data);
        }

        shop.RecordLastPriceSnapshot();
        await Context.SaveChangesAsync();
        return Ok();
    }

    [HttpGet("shapshot")]
    [ProducesResponseType(typeof(List<PriceDto>), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    public async Task<IActionResult> GetSnapshot()
    {
        var shop = await GetShop();
        var snapshot = await Context.Set<PriceSnapshot>().FindAsync(shop.Id);
        if (snapshot?.Data is null)
            return NotFound();

        var nomenclatures = JsonSerializer.Deserialize<List<PriceDto>>(snapshot.Data);
        var dtos = mapper.Map<List<PriceDto>>(nomenclatures);
        return Ok(dtos);
    }

    [HttpGet]
    [ProducesResponseType(typeof(List<PriceDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetPrices()
    {
        var shop = await GetShop();
        List<PriceDto>? result = null;
        if (shop.LastPriceSyncTime == null || shop.LastPriceSyncTime.Value.Day != DateTimeOffset.UtcNow.Day)
        {
            result = await SyncPrices();
            return Ok(result);
        }

        var nomenclatures = await Context.FullNomenclatures.Where(n => n.ShopId == shop.Id)
            .OrderBy(n => n.Name)
            .ToListAsync();

        await FetchEstimatedTaxes(shop, nomenclatures);

        nomenclatures.LeaveOnlyWithTokens(shop);
        result ??= mapper.Map<List<PriceDto>>(nomenclatures.SortByWierdRules());

        if (result != null)
        {
            for (int i = 0; i < result.Count; i++)
            {
                var nomenclature = nomenclatures.SortByWierdRules().ElementAtOrDefault(i);
                if (nomenclature != null)
                {
                    result[i].CalculateProfitFields(nomenclature, shop);
                }
            }
        }

        return Ok(result);
    }

    private async Task FetchEstimatedTaxes(Shop shop, List<Nomenclature> nomenclatures)
    {
        try
        {
            if (shop.WbKey != null && (shop.LastWildberriesEstimation is null
                                       || shop.LastWildberriesEstimation.Value.Day != DateTimeOffset.UtcNow.Day
                                       || shop.LastWildberriesEstimation.Value.Month != DateTimeOffset.UtcNow.Month))
            {
                var dateTo = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.UtcNow.Day);
                var dateFrom = dateTo.AddMonths(-1).AddDays(-1);
                var wbReport = await wbClient.GetReportDtos(
                    shop.GetToken(Marketplace.Wildberries),
                    dateFrom,
                    dateTo);

                var wbGeneralCommission = NomenclatureAggregator.UpsertTaxes(nomenclatures, wbReport);
                shop.SetWbEstimatedCommission(wbGeneralCommission);
            }
        }
        catch (Exception e)
        {
            logger.LogError(e, "Failed to fetch Wildberries estimated taxes");
        }

        try
        {
            if (shop.OzonKey != null && (shop.LastOzonEstimation is null
                || shop.LastOzonEstimation.Value.Day != DateTimeOffset.UtcNow.Day
                || shop.LastOzonEstimation.Value.Month != DateTimeOffset.UtcNow.Month))
            {
                var dateTo = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.UtcNow.Day);
                dateTo = DateTime.SpecifyKind(dateTo, DateTimeKind.Utc);
                var dateFrom = DateTime.SpecifyKind(dateTo.AddMonths(-1), DateTimeKind.Utc);
                var ozonReport = await ozonClient.GetReportDtos(shop, dateFrom, dateTo);
                var ozonEstimatedCommission = NomenclatureAggregator.UpsertTaxes(nomenclatures, ozonReport);
                shop.SetOzonEstimatedCommission(ozonEstimatedCommission);
            }
        }
        catch (Exception e)
        {
            logger.LogError(e, "Failed to fetch ozon estimated taxes");
        }

        await Context.SaveChangesAsync();
    }

    [HttpPut]
    public async Task<IActionResult> UpdatePrices([FromBody] UpdatePricesBody body)
    {
        var shop = await GetShop();

        var nomenclatures = await Context.FullNomenclatures
            .Where(n => n.ShopId == shop.Id)
            .ToListAsync();

        var ozonPrices = new List<(string Code, decimal Price)>();
        var wbPrices = new List<(int Id, decimal Discount, decimal Price)>();
        var yandexPrices = new List<(string Code, decimal Price)>();
        foreach (var item in body.Prices)
        {
            var nomenclature = nomenclatures.Find(n => n.Id == item.Id);
            if (nomenclature is null)
            {
                continue;
            }

            nomenclature.UpdatePurchasePrice(item.PurchasePrice);

            if (nomenclature.Ozon != null
                && item.OzonPrice != null
                && item.OzonPrice != nomenclature.Ozon.Price)
            {
                ozonPrices.Add((nomenclature.Ozon.Code, item.OzonPrice.Value));
            }

            if (nomenclature.Wildberries != null
                && (item.WildberriesPrice != nomenclature.Wildberries.Price
                    || item.WildberriesDiscountPercent != nomenclature.Wildberries.Discount))
            {
                // user changes only discount or price
                item.WildberriesDiscountPercent ??= nomenclature.Wildberries.Discount;
                item.WildberriesPrice ??= nomenclature.Wildberries.Price;

                // in case of null values, skip the item
                if (item.WildberriesDiscountPercent is null || item.WildberriesPrice is null)
                {
                    continue;
                }

                wbPrices.Add((nomenclature.Wildberries.Id,
                    item.WildberriesDiscountPercent.Value,
                    item.WildberriesPrice.Value));
                nomenclature.UpdateWildberriesPrice(item.WildberriesPrice.Value);
                nomenclature.UpdateWildberriesDiscount(item.WildberriesDiscountPercent.Value);
            }

            if (nomenclature.Yandex != null
                && item.YandexPrice != null
                && item.YandexPrice != nomenclature.Yandex.Price)
            {
                yandexPrices.Add((nomenclature.Yandex.Code, item.YandexPrice.Value));
                nomenclature.UpdateYandexPrice(item.YandexPrice.Value);
            }
        }

        await Task.WhenAll(
            ozonClient.UpdatePrice(shop, ozonPrices),
            wbClient.UpdatePrice(shop.GetToken(Marketplace.Wildberries), wbPrices),
            yandexClient.UpdatePrice(shop.GetToken(Marketplace.Yandex), yandexPrices)
        );

        await Task.Delay(500);
        await SyncPrices();
        await Context.SaveChangesAsync();
        return Ok();
    }

    [HttpGet("/shops/current/nomenclatures/{nomenclatureId}/ozon/promotes")]
    public async Task<IActionResult> GetPromotesForNomenclature(int nomenclatureId)
    {
        var shop = await GetShop();
        var nomenclature = await Context.FullNomenclatures
            .Where(n => n.ShopId == shop.Id)
            .FirstOrDefaultAsync(n => n.Id == nomenclatureId);

        if (nomenclature?.Ozon is null)
        {
            throw new NotFoundException("Ozon товар не найден");
        }

        var promote = (await ozonClient.GetPrices(shop, [nomenclature.Ozon.Code])).FirstOrDefault();
        if (promote is null)
        {
            throw new BadRequestException("Промоакции не найдены");
        }

        var promotes = await ozonClient.GetAvailablePromotes(shop);
        // filter only available promotes
        var promotes1 = promote.MarketingPromotes.Actions.Where(p => promotes.Any(pr => pr.Title == p.Title)).ToList();
        var dtos = mapper.Map<List<PromoteDto>>(promotes1);
        // take ids from all available promotes
        dtos.ForEach(d => d.Id = promotes.FirstOrDefault(p => p.Title == d.Title)?.Id ?? 0);
        return Ok(dtos);
    }


    [HttpDelete("/shops/current/nomenclatures/{nomenclatureId}/ozon/promotes/{promoteId}")]
    public async Task<IActionResult> DeletePromoteForNomenclature(int nomenclatureId, int promoteId)
    {
        var shop = await GetShop();
        var nomenclature = await Context.FullNomenclatures
            .Where(n => n.ShopId == shop.Id)
            .FirstOrDefaultAsync(n => n.Id == nomenclatureId);

        if (nomenclature?.Ozon is null)
        {
            throw new NotFoundException("Ozon товар не найден");
        }

        await ozonClient.DeletePromote(shop, nomenclature.Ozon.Id, promoteId);
        await Task.Delay(500);
        var dtos = await SyncPrices();
        var dto = dtos.Find(f => f.Id == nomenclatureId);
        return Ok(dto);
    }

    [HttpPost("calculate-discount")]
    [ProducesResponseType(typeof(CalculateDiscountResponse), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> CalculateDiscount([FromBody] CalculateDiscountRequest request)
    {
        var shop = await GetShop();

        var nomenclature = await Context.FullNomenclatures
            .Where(n => n.ShopId == shop.Id && n.Id == request.NomenclatureId)
            .FirstOrDefaultAsync() ?? throw new NotFoundException("Nomenclature not found");

        var minimumRevenue = nomenclature.MinimumRevenue ?? shop.MinimumRevenue ?? throw new InvalidOperationException("No min revenue defined");
        var minimumProfitPercentage = nomenclature.MinimumProfitPercentage ?? shop.MinimumProfitPercentage ?? throw new InvalidOperationException("No min profit percentage defined");

        var revenueSource = nomenclature.MinimumRevenue.HasValue ? "nomenclature" :
                           shop.MinimumRevenue.HasValue ? "shop" : "default";
        var profitPercentageSource = nomenclature.MinimumProfitPercentage.HasValue ? "nomenclature" :
                                   shop.MinimumProfitPercentage.HasValue ? "shop" : "default";

        decimal bankTax = shop.BankTax ?? default;
        decimal marketplaceTax = shop.GetMarketplaceTax(request.Marketplace) ?? throw new InvalidOperationException("No marketplace tax defined");

        var (calculatedDiscount, discountForMinProfit, discountForMinProfitPercentage) =
            discountCalculationService.CalculateOptimalDiscount(
                request.BasePrice,
                request.PurchasePrice,
                minimumRevenue,
                minimumProfitPercentage,
                bankTax,
                marketplaceTax);

        return Ok(new CalculateDiscountResponse
        {
            CalculatedDiscountPercent = calculatedDiscount,
            DiscountForMinimumProfit = discountForMinProfit,
            DiscountForMinimumProfitPercentage = discountForMinProfitPercentage,
            UsedMinimumRevenue = minimumRevenue,
            UsedMinimumProfitPercentage = minimumProfitPercentage,
            MinimumRevenueSource = revenueSource,
            MinimumProfitPercentageSource = profitPercentageSource
        });
    }

    [HttpPost("calculate")]
    [ProducesResponseType(typeof(CalculatePriceResponse), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    public async Task<IActionResult> CalculatePrice([FromBody] CalculatePriceRequest request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var shop = await GetShop();

        var nomenclature = await Context.FullNomenclatures
            .Where(n => n.ShopId == shop.Id && n.Id == request.NomenclatureId)
            .FirstOrDefaultAsync() ?? throw new NotFoundException("Nomenclature not found");

        var nestedPriceDto = new NestedPriceDto
        {
            BasePrice = request.BasePrice,
            DiscountPercent = request.DiscountPercent,
        };

        switch (request.Marketplace)
        {
            case Marketplace.Ozon:
                nestedPriceDto.SalePrice = nomenclature.Ozon!.AnyActiveMarketingCampaigns
                    ? nomenclature.Ozon!.MarketingSellerPrice
                    : request.BasePrice;
                break;
            case Marketplace.Wildberries:
            case Marketplace.Yandex:
                // For Wildberries and Yandex, SalePrice will be calculated from BasePrice and DiscountPercent
                break;
            default:
                throw new ArgumentException("Invalid marketplace");
        }

        var marketplaceTax = shop.GetMarketplaceTax(request.Marketplace) ?? throw new InvalidOperationException("No marketplace tax defined");
        nestedPriceDto.CalculateProfitFields(request.PurchasePrice, shop.BankTax, marketplaceTax);

        return Ok(new CalculatePriceResponse
        {
            PurchasePrice = request.PurchasePrice,
            BasePrice = request.BasePrice,
            DiscountPercent = request.DiscountPercent,
            SalePrice = nestedPriceDto.SalePrice ?? throw new InvalidOperationException("Sale price unavailable"),
            BankCommissionAmount = nestedPriceDto.BankCommissionAmount ?? throw new InvalidOperationException("Bank commission unavailable"),
            MarketplaceCommissionAmount = nestedPriceDto.MarketplaceCommissionAmount ?? throw new InvalidOperationException("Marketplace commission unavailable"),
            Revenue = nestedPriceDto.Revenue ?? throw new InvalidOperationException("Revenue unavailable"),
            Profit = nestedPriceDto.Profit ?? throw new InvalidOperationException("Profit unavailable"),
            ProfitPercentage = nestedPriceDto.ProfitPercentage ?? throw new InvalidOperationException("Profit percentage unavailable")
        });
    }
}